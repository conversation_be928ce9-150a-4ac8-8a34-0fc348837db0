<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理面板主题 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            color: #333;
        }
        
        .theme-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
        }
        
        .theme-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eaedf0;
        }
        
        .theme-header h1 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }
        
        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .theme-card {
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.2s ease;
            cursor: pointer;
            border: 2px solid #eaedf0;
            overflow: hidden;
        }
        
        .theme-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .theme-card.selected {
            border-color: #3182ce;
        }
        
        .theme-preview {
            height: 200px;
            position: relative;
            overflow: hidden;
        }
        
        .theme-info {
            padding: 15px;
        }
        
        .theme-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2d3748;
        }
        
        .theme-description {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .theme-colors {
            display: flex;
            gap: 8px;
        }
        
        .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .apply-button {
            background: #3182ce;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: block;
            margin: 20px auto;
        }
        
        .apply-button:hover {
            background: #2b6cb0;
        }
        
        /* 主题预览样式 */
        .preview-container {
            display: flex;
            height: 100%;
        }
        
        .preview-sidebar {
            width: 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 15px;
            gap: 15px;
        }
        
        .preview-main {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        
        .preview-header {
            height: 40px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            padding: 0 15px;
            border-radius: 4px;
        }
        
        .preview-content {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .preview-card {
            border-radius: 4px;
            height: 60px;
        }
        
        .preview-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
        }
        
        /* 主题1: 经典管理面板 */
        .theme-1 .preview-sidebar {
            background-color: #1a202c;
        }
        
        .theme-1 .preview-icon {
            background-color: #4a5568;
        }
        
        .theme-1 .preview-main {
            background-color: #f7fafc;
        }
        
        .theme-1 .preview-header {
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
        }
        
        .theme-1 .preview-card {
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
        }
        
        /* 主题2: 现代简洁风格 */
        .theme-2 .preview-sidebar {
            background-color: #ffffff;
            border-right: 1px solid #edf2f7;
        }
        
        .theme-2 .preview-icon {
            background-color: #ebf8ff;
        }
        
        .theme-2 .preview-main {
            background-color: #ffffff;
        }
        
        .theme-2 .preview-header {
            background-color: #ffffff;
            border-bottom: 1px solid #edf2f7;
        }
        
        .theme-2 .preview-card {
            background-color: #f7fafc;
            border: 1px solid #edf2f7;
        }
        
        /* 主题3: 专业深色主题 */
        .theme-3 .preview-sidebar {
            background-color: #1a1a1a;
        }
        
        .theme-3 .preview-icon {
            background-color: #333333;
        }
        
        .theme-3 .preview-main {
            background-color: #252525;
        }
        
        .theme-3 .preview-header {
            background-color: #2d2d2d;
        }
        
        .theme-3 .preview-card {
            background-color: #2d2d2d;
            border: 1px solid #333333;
        }
        
        /* 主题4: 传统商务风格 */
        .theme-4 .preview-sidebar {
            background-color: #2c3e50;
        }
        
        .theme-4 .preview-icon {
            background-color: #34495e;
        }
        
        .theme-4 .preview-main {
            background-color: #ecf0f1;
        }
        
        .theme-4 .preview-header {
            background-color: #ffffff;
        }
        
        .theme-4 .preview-card {
            background-color: #ffffff;
            border: 1px solid #bdc3c7;
        }
        
        /* 主题5: 安全主题 */
        .theme-5 .preview-sidebar {
            background-color: #1e3a2f;
        }
        
        .theme-5 .preview-icon {
            background-color: #2d5f4e;
        }
        
        .theme-5 .preview-main {
            background-color: #f0f9f6;
        }
        
        .theme-5 .preview-header {
            background-color: #ffffff;
        }
        
        .theme-5 .preview-card {
            background-color: #ffffff;
            border: 1px solid #d1e7dd;
        }
        
        /* 主题6: 数据分析主题 */
        .theme-6 .preview-sidebar {
            background-color: #1e3a8a;
        }
        
        .theme-6 .preview-icon {
            background-color: #2563eb;
        }
        
        .theme-6 .preview-main {
            background-color: #f8fafc;
        }
        
        .theme-6 .preview-header {
            background-color: #ffffff;
        }
        
        .theme-6 .preview-card {
            background-color: #ffffff;
            border: 1px solid #dbeafe;
        }
    </style>
</head>
<body>
    <div class="theme-container">
        <div class="theme-header">
            <h1>管理面板主题选择</h1>
            <p class="text-muted">为蜜罐管理平台选择一个专业、实用的配色方案</p>
        </div>
        
        <div class="theme-grid">
            <!-- 主题1: 经典管理面板 -->
            <div class="theme-card" data-theme="classic-admin">
                <div class="theme-preview theme-1">
                    <div class="preview-container">
                        <div class="preview-sidebar">
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                        </div>
                        <div class="preview-main">
                            <div class="preview-header"></div>
                            <div class="preview-content">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="theme-info">
                    <h3 class="theme-title">经典管理面板</h3>
                    <p class="theme-description">深色侧边栏 + 白色主体，经典实用的管理界面</p>
                    <div class="theme-colors">
                        <div class="color-dot" style="background: #1a202c;"></div>
                        <div class="color-dot" style="background: #ffffff;"></div>
                        <div class="color-dot" style="background: #3182ce;"></div>
                        <div class="color-dot" style="background: #f7fafc;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 主题2: 现代简洁风格 -->
            <div class="theme-card" data-theme="modern-clean">
                <div class="theme-preview theme-2">
                    <div class="preview-container">
                        <div class="preview-sidebar">
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                        </div>
                        <div class="preview-main">
                            <div class="preview-header"></div>
                            <div class="preview-content">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="theme-info">
                    <h3 class="theme-title">现代简洁风格</h3>
                    <p class="theme-description">全白背景，简洁明了，适合长时间使用</p>
                    <div class="theme-colors">
                        <div class="color-dot" style="background: #ffffff;"></div>
                        <div class="color-dot" style="background: #edf2f7;"></div>
                        <div class="color-dot" style="background: #4299e1;"></div>
                        <div class="color-dot" style="background: #ebf8ff;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 主题3: 专业深色主题 -->
            <div class="theme-card" data-theme="pro-dark">
                <div class="theme-preview theme-3">
                    <div class="preview-container">
                        <div class="preview-sidebar">
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                        </div>
                        <div class="preview-main">
                            <div class="preview-header"></div>
                            <div class="preview-content">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="theme-info">
                    <h3 class="theme-title">专业深色主题</h3>
                    <p class="theme-description">全深色界面，减少眼睛疲劳，适合夜间使用</p>
                    <div class="theme-colors">
                        <div class="color-dot" style="background: #1a1a1a;"></div>
                        <div class="color-dot" style="background: #252525;"></div>
                        <div class="color-dot" style="background: #0ea5e9;"></div>
                        <div class="color-dot" style="background: #f97316;"></div>
                    </div>
                </div>
            </div>

            <!-- 主题4: 传统商务风格 -->
            <div class="theme-card" data-theme="business-classic">
                <div class="theme-preview theme-4">
                    <div class="preview-container">
                        <div class="preview-sidebar">
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                        </div>
                        <div class="preview-main">
                            <div class="preview-header"></div>
                            <div class="preview-content">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="theme-info">
                    <h3 class="theme-title">传统商务风格</h3>
                    <p class="theme-description">保守稳重的商务配色，适合企业级应用</p>
                    <div class="theme-colors">
                        <div class="color-dot" style="background: #2c3e50;"></div>
                        <div class="color-dot" style="background: #ecf0f1;"></div>
                        <div class="color-dot" style="background: #3498db;"></div>
                        <div class="color-dot" style="background: #bdc3c7;"></div>
                    </div>
                </div>
            </div>

            <!-- 主题5: 安全主题 -->
            <div class="theme-card" data-theme="security-green">
                <div class="theme-preview theme-5">
                    <div class="preview-container">
                        <div class="preview-sidebar">
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                        </div>
                        <div class="preview-main">
                            <div class="preview-header"></div>
                            <div class="preview-content">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="theme-info">
                    <h3 class="theme-title">安全主题</h3>
                    <p class="theme-description">绿色调配色，突出安全和稳定的概念</p>
                    <div class="theme-colors">
                        <div class="color-dot" style="background: #1e3a2f;"></div>
                        <div class="color-dot" style="background: #f0f9f6;"></div>
                        <div class="color-dot" style="background: #059669;"></div>
                        <div class="color-dot" style="background: #d1e7dd;"></div>
                    </div>
                </div>
            </div>

            <!-- 主题6: 数据分析主题 -->
            <div class="theme-card" data-theme="data-analysis">
                <div class="theme-preview theme-6">
                    <div class="preview-container">
                        <div class="preview-sidebar">
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                            <div class="preview-icon"></div>
                        </div>
                        <div class="preview-main">
                            <div class="preview-header"></div>
                            <div class="preview-content">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="theme-info">
                    <h3 class="theme-title">数据分析主题</h3>
                    <p class="theme-description">蓝色调配色，适合数据展示和分析界面</p>
                    <div class="theme-colors">
                        <div class="color-dot" style="background: #1e3a8a;"></div>
                        <div class="color-dot" style="background: #f8fafc;"></div>
                        <div class="color-dot" style="background: #2563eb;"></div>
                        <div class="color-dot" style="background: #dbeafe;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="apply-button" onclick="applySelectedTheme()">
                应用选中的主题
            </button>
            <p style="color: #718096; margin-top: 10px; font-size: 0.9rem;">
                选择一个主题后点击应用，我将为您生成对应的demo页面
            </p>
        </div>
    </div>

    <script>
        let selectedTheme = null;

        // 主题选择
        document.querySelectorAll('.theme-card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除之前的选中状态
                document.querySelectorAll('.theme-card').forEach(c => c.classList.remove('selected'));
                // 添加选中状态
                this.classList.add('selected');
                selectedTheme = this.dataset.theme;
            });
        });

        // 应用主题
        function applySelectedTheme() {
            if (!selectedTheme) {
                alert('请先选择一个主题！');
                return;
            }

            const themeNames = {
                'classic-admin': '经典管理面板',
                'modern-clean': '现代简洁风格',
                'pro-dark': '专业深色主题',
                'business-classic': '传统商务风格',
                'security-green': '安全主题',
                'data-analysis': '数据分析主题'
            };

            alert(`您选择了: ${themeNames[selectedTheme]}\n\n我将为您生成对应的demo页面！`);

            // 这里可以调用生成对应主题demo页面的功能
            console.log('生成主题:', selectedTheme);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.theme-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.4s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
