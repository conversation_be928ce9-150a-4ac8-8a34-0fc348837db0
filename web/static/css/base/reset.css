/* 现代化CSS重置 */

/* 1. 使用更好的盒模型 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 2. 移除默认边距 */
* {
  margin: 0;
}

/* 3. 改善行高和文本渲染 */
body {
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 4. 改善媒体元素的默认样式 */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

/* 5. 移除表单元素的默认样式 */
input,
button,
textarea,
select {
  font: inherit;
}

/* 6. 避免文本溢出 */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

/* 7. 创建根堆叠上下文 */
#root,
#__next {
  isolation: isolate;
}

/* 8. 改善列表样式 */
ul,
ol {
  list-style: none;
  padding: 0;
}

/* 9. 移除链接的默认样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 10. 改善按钮样式 */
button {
  border: none;
  background: none;
  cursor: pointer;
}

/* 11. 改善表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 12. 改善表单元素的可访问性 */
input:focus,
textarea:focus,
select:focus,
button:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 13. 隐藏元素的辅助类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 14. 改善滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* 15. 改善选择文本的样式 */
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-900);
}

/* 16. 减少动画对于偏好减少动画的用户 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
