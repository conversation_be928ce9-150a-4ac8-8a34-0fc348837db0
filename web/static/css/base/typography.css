/* 字体排版系统 */

/* 基础字体设置 */
body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-dark);
  background-color: var(--bg-light);
}

/* 标题样式 */
h1, .h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-4);
  color: var(--text-dark);
}

h2, .h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-3);
  color: var(--text-dark);
}

h3, .h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-3);
  color: var(--text-dark);
}

h4, .h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-2);
  color: var(--text-dark);
}

h5, .h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-2);
  color: var(--text-dark);
}

h6, .h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-2);
  color: var(--text-dark);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 段落样式 */
p {
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
}

/* 文本大小工具类 */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

/* 字体粗细工具类 */
.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.font-extrabold {
  font-weight: var(--font-weight-extrabold);
}

/* 文本颜色工具类 */
.text-primary {
  color: var(--text-primary);
}

.text-success {
  color: var(--text-success);
}

.text-warning {
  color: var(--text-warning);
}

.text-danger {
  color: var(--text-danger);
}

.text-info {
  color: var(--text-info);
}

.text-dark {
  color: var(--text-dark);
}

.text-muted {
  color: var(--text-muted);
}

.text-light {
  color: var(--text-light);
}

.text-white {
  color: #ffffff;
}

/* 文本对齐工具类 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

/* 文本转换工具类 */
.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

/* 行高工具类 */
.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

/* 字母间距工具类 */
.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

/* 文本装饰工具类 */
.no-underline {
  text-decoration: none;
}

.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

/* 文本溢出处理 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

/* 换行控制 */
.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

/* 单词断行 */
.break-normal {
  overflow-wrap: normal;
  word-break: normal;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

/* 代码样式 */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
  color: var(--color-gray-800);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--color-gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  line-height: var(--line-height-relaxed);
}

pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* 引用样式 */
blockquote {
  border-left: 4px solid var(--color-primary-200);
  padding-left: var(--space-4);
  margin: var(--space-4) 0;
  font-style: italic;
  color: var(--text-muted);
}

/* 链接样式 */
a {
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-700);
}

a:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 列表样式 */
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-1);
}

/* 水平分割线 */
hr {
  border: none;
  height: 1px;
  background-color: var(--border-color);
  margin: var(--space-8) 0;
}
