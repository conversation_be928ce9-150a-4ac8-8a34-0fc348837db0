/* CSS变量定义 - 统一设计系统 */
:root {
  /* 颜色系统 */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e9ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #667eea;
  --color-primary-600: #5b21b6;
  --color-primary-700: #4c1d95;
  --color-primary-800: #3730a3;
  --color-primary-900: #1e3a8a;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #1cc88a;
  --color-success-600: #16a34a;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-500: #f6c23e;
  --color-warning-600: #d97706;
  --color-warning-900: #92400e;

  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-500: #e74a3b;
  --color-danger-600: #dc2626;
  --color-danger-900: #991b1b;

  --color-info-50: #f0f9ff;
  --color-info-100: #e0f2fe;
  --color-info-500: #36b9cc;
  --color-info-600: #0284c7;
  --color-info-900: #0c4a6e;

  /* 中性色系 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 语义化颜色 */
  --color-primary: var(--color-primary-500);
  --color-success: var(--color-success-500);
  --color-warning: var(--color-warning-500);
  --color-danger: var(--color-danger-500);
  --color-info: var(--color-info-500);

  /* 背景色 */
  --bg-primary: var(--color-primary-500);
  --bg-success: var(--color-success-500);
  --bg-warning: var(--color-warning-500);
  --bg-danger: var(--color-danger-500);
  --bg-info: var(--color-info-500);
  --bg-light: #f8f9fc;
  --bg-white: #ffffff;
  --bg-dark: var(--color-gray-800);

  /* 文本颜色 */
  --text-primary: var(--color-primary-600);
  --text-success: var(--color-success-600);
  --text-warning: var(--color-warning-600);
  --text-danger: var(--color-danger-600);
  --text-info: var(--color-info-600);
  --text-dark: var(--color-gray-800);
  --text-muted: var(--color-gray-500);
  --text-light: var(--color-gray-300);

  /* 边框颜色 */
  --border-color: #e3e6f0;
  --border-primary: var(--color-primary-200);
  --border-success: var(--color-success-200);
  --border-warning: var(--color-warning-200);
  --border-danger: var(--color-danger-200);
  --border-info: var(--color-info-200);

  /* 间距系统 (基于8px网格) */
  --space-0: 0;
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
  --space-20: 5rem;    /* 80px */

  /* 字体系统 */
  --font-family-sans: 'Inter', 'Segoe UI', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* 圆角系统 */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* 图标尺寸 */
  --icon-xs: 0.75rem;   /* 12px */
  --icon-sm: 1rem;      /* 16px */
  --icon-base: 1.25rem; /* 20px */
  --icon-lg: 1.5rem;    /* 24px */
  --icon-xl: 2rem;      /* 32px */
  --icon-2xl: 2.5rem;   /* 40px */
  --icon-3xl: 3rem;     /* 48px */

  /* 容器最大宽度 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-light: #1f2937;
    --bg-white: #374151;
    --text-dark: #f9fafb;
    --text-muted: #d1d5db;
    --border-color: #4b5563;
  }
}
