/* 卡片组件样式 */

/* 基础卡片样式 */
.card {
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 卡片头部 */
.card-header {
  padding: var(--space-4) var(--space-6);
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--border-color);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.card-header-primary {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  color: white;
  border-bottom: none;
}

.card-header-clean {
  background-color: transparent;
  border-bottom: none;
}

/* 卡片主体 */
.card-body {
  padding: var(--space-6);
}

.card-body-sm {
  padding: var(--space-4);
}

.card-body-lg {
  padding: var(--space-8);
}

/* 卡片底部 */
.card-footer {
  padding: var(--space-4) var(--space-6);
  background-color: var(--color-gray-50);
  border-top: 1px solid var(--border-color);
}

/* 统计卡片 */
.stat-card {
  position: relative;
  background: white;
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-primary-500);
}

.stat-card-primary::before {
  background-color: var(--color-primary-500);
}

.stat-card-success::before {
  background-color: var(--color-success-500);
}

.stat-card-warning::before {
  background-color: var(--color-warning-500);
}

.stat-card-danger::before {
  background-color: var(--color-danger-500);
}

.stat-card-info::before {
  background-color: var(--color-info-500);
}

/* 统计卡片内容 */
.stat-card-body {
  padding: var(--space-5) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-card-content {
  flex: 1;
}

.stat-card-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
  color: var(--text-muted);
}

.stat-card-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  line-height: 1;
}

.stat-card-icon {
  flex-shrink: 0;
  margin-left: var(--space-4);
}

/* 图表卡片 */
.chart-card {
  background: white;
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.chart-card-header {
  padding: var(--space-4) var(--space-6);
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chart-card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.chart-card-body {
  padding: var(--space-6);
}

.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
}

.chart-container-sm {
  height: 200px;
}

.chart-container-lg {
  height: 400px;
}

/* 信息卡片 */
.info-card {
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-primary-100) 100%);
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.info-card-success {
  background: linear-gradient(135deg, var(--color-success-50) 0%, var(--color-success-100) 100%);
  border-color: var(--color-success-200);
}

.info-card-warning {
  background: linear-gradient(135deg, var(--color-warning-50) 0%, var(--color-warning-100) 100%);
  border-color: var(--color-warning-200);
}

.info-card-danger {
  background: linear-gradient(135deg, var(--color-danger-50) 0%, var(--color-danger-100) 100%);
  border-color: var(--color-danger-200);
}

/* 操作卡片 */
.action-card {
  background: white;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-base);
  cursor: pointer;
}

.action-card:hover {
  border-color: var(--color-primary-300);
  background-color: var(--color-primary-50);
}

.action-card-icon {
  font-size: var(--icon-3xl);
  color: var(--text-light);
  margin-bottom: var(--space-4);
}

.action-card:hover .action-card-icon {
  color: var(--color-primary-500);
}

.action-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin-bottom: var(--space-2);
}

.action-card-description {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 紧凑卡片 */
.compact-card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  box-shadow: var(--shadow-xs);
}

/* 卡片网格 */
.card-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid-sm {
  gap: var(--space-4);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.card-grid-lg {
  gap: var(--space-8);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* 卡片状态 */
.card-loading {
  opacity: 0.6;
  pointer-events: none;
}

.card-disabled {
  opacity: 0.5;
  pointer-events: none;
  filter: grayscale(1);
}

/* 卡片动画 */
.card-fade-in {
  animation: cardFadeIn 0.5s ease-out;
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-body {
    padding: var(--space-4);
  }
  
  .card-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .stat-card-body {
    padding: var(--space-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .stat-card-icon {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .stat-card-value {
    font-size: var(--font-size-xl);
  }
  
  .chart-container {
    height: 250px;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
}
