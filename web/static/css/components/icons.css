/* 图标组件样式 */

/* 基础图标样式 */
.icon {
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标尺寸工具类 */
.icon-xs {
  font-size: var(--icon-xs);
  width: var(--icon-xs);
  height: var(--icon-xs);
}

.icon-sm {
  font-size: var(--icon-sm);
  width: var(--icon-sm);
  height: var(--icon-sm);
}

.icon-base {
  font-size: var(--icon-base);
  width: var(--icon-base);
  height: var(--icon-base);
}

.icon-lg {
  font-size: var(--icon-lg);
  width: var(--icon-lg);
  height: var(--icon-lg);
}

.icon-xl {
  font-size: var(--icon-xl);
  width: var(--icon-xl);
  height: var(--icon-xl);
}

.icon-2xl {
  font-size: var(--icon-2xl);
  width: var(--icon-2xl);
  height: var(--icon-2xl);
}

.icon-3xl {
  font-size: var(--icon-3xl);
  width: var(--icon-3xl);
  height: var(--icon-3xl);
}

/* 统计卡片中的大图标 */
.stat-icon {
  font-size: var(--icon-2xl);
  color: var(--text-light);
  transition: color var(--transition-fast);
}

.stat-icon-primary {
  color: var(--color-primary-300);
}

.stat-icon-success {
  color: var(--color-success-300);
}

.stat-icon-warning {
  color: var(--color-warning-300);
}

.stat-icon-danger {
  color: var(--color-danger-300);
}

.stat-icon-info {
  color: var(--color-info-300);
}

/* 导航图标 */
.nav-icon {
  font-size: var(--icon-base);
  margin-right: var(--space-2);
  vertical-align: middle;
}

/* 按钮图标 */
.btn-icon {
  font-size: var(--icon-sm);
  margin-right: var(--space-2);
  vertical-align: middle;
}

.btn-icon-only {
  font-size: var(--icon-base);
  margin: 0;
}

/* 表格图标 */
.table-icon {
  font-size: var(--icon-sm);
  vertical-align: middle;
}

/* 状态图标 */
.status-icon {
  font-size: var(--icon-sm);
  margin-right: var(--space-1);
  vertical-align: middle;
}

.status-icon-online {
  color: var(--color-success-500);
}

.status-icon-offline {
  color: var(--color-danger-500);
}

.status-icon-warning {
  color: var(--color-warning-500);
}

.status-icon-pending {
  color: var(--color-info-500);
}

/* 加载图标动画 */
.icon-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 图标容器 */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background-color: var(--color-gray-100);
  transition: background-color var(--transition-fast);
}

.icon-container-xs {
  width: calc(var(--icon-xs) + var(--space-2));
  height: calc(var(--icon-xs) + var(--space-2));
}

.icon-container-sm {
  width: calc(var(--icon-sm) + var(--space-3));
  height: calc(var(--icon-sm) + var(--space-3));
}

.icon-container-base {
  width: calc(var(--icon-base) + var(--space-4));
  height: calc(var(--icon-base) + var(--space-4));
}

.icon-container-lg {
  width: calc(var(--icon-lg) + var(--space-5));
  height: calc(var(--icon-lg) + var(--space-5));
}

.icon-container-xl {
  width: calc(var(--icon-xl) + var(--space-6));
  height: calc(var(--icon-xl) + var(--space-6));
}

/* 图标容器颜色变体 */
.icon-container-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-600);
}

.icon-container-success {
  background-color: var(--color-success-100);
  color: var(--color-success-600);
}

.icon-container-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-600);
}

.icon-container-danger {
  background-color: var(--color-danger-100);
  color: var(--color-danger-600);
}

.icon-container-info {
  background-color: var(--color-info-100);
  color: var(--color-info-600);
}

/* 图标与文本的间距 */
.icon-text {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.icon-text-sm {
  gap: var(--space-1);
}

.icon-text-lg {
  gap: var(--space-3);
}

/* 图标悬停效果 */
.icon-hover {
  transition: all var(--transition-fast);
}

.icon-hover:hover {
  transform: scale(1.1);
  color: var(--color-primary-600);
}

/* 图标徽章 */
.icon-badge {
  position: relative;
  display: inline-block;
}

.icon-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: var(--color-danger-500);
  border-radius: var(--radius-full);
  border: 2px solid white;
}

.icon-badge-success::after {
  background-color: var(--color-success-500);
}

.icon-badge-warning::after {
  background-color: var(--color-warning-500);
}

.icon-badge-info::after {
  background-color: var(--color-info-500);
}

/* 图标分组 */
.icon-group {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.icon-group-sm {
  gap: var(--space-1);
}

.icon-group-lg {
  gap: var(--space-3);
}

/* 响应式图标 */
@media (max-width: 768px) {
  .stat-icon {
    font-size: var(--icon-xl);
  }
  
  .icon-2xl {
    font-size: var(--icon-xl);
  }
  
  .icon-3xl {
    font-size: var(--icon-2xl);
  }
}
