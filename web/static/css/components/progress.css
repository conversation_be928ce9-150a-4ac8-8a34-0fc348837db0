/* 进度条组件样式 */

/* 基础进度条 */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-sm {
  height: 4px;
}

.progress-lg {
  height: 12px;
}

.progress-xl {
  height: 16px;
}

/* 进度条填充 */
.progress-bar {
  height: 100%;
  background-color: var(--color-primary-500);
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
  position: relative;
  overflow: hidden;
}

/* 进度条颜色变体 */
.progress-bar-primary {
  background-color: var(--color-primary-500);
}

.progress-bar-success {
  background-color: var(--color-success-500);
}

.progress-bar-warning {
  background-color: var(--color-warning-500);
}

.progress-bar-danger {
  background-color: var(--color-danger-500);
}

.progress-bar-info {
  background-color: var(--color-info-500);
}

/* 渐变进度条 */
.progress-bar-gradient-primary {
  background: linear-gradient(90deg, var(--color-primary-400), var(--color-primary-600));
}

.progress-bar-gradient-success {
  background: linear-gradient(90deg, var(--color-success-400), var(--color-success-600));
}

.progress-bar-gradient-warning {
  background: linear-gradient(90deg, var(--color-warning-400), var(--color-warning-600));
}

.progress-bar-gradient-danger {
  background: linear-gradient(90deg, var(--color-danger-400), var(--color-danger-600));
}

.progress-bar-gradient-info {
  background: linear-gradient(90deg, var(--color-info-400), var(--color-info-600));
}

/* 动画进度条 */
.progress-bar-animated {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
  animation: progressBarStripes 1s linear infinite;
}

@keyframes progressBarStripes {
  0% {
    background-position-x: 1rem;
  }
  100% {
    background-position-x: 0;
  }
}

/* 脉冲动画 */
.progress-bar-pulse {
  animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 进度条标签 */
.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.progress-label-text {
  color: var(--text-dark);
}

.progress-label-value {
  color: var(--text-muted);
  font-weight: var(--font-weight-semibold);
}

/* 圆形进度条 */
.progress-circle {
  position: relative;
  width: 80px;
  height: 80px;
}

.progress-circle-sm {
  width: 60px;
  height: 60px;
}

.progress-circle-lg {
  width: 100px;
  height: 100px;
}

.progress-circle-xl {
  width: 120px;
  height: 120px;
}

.progress-circle svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-circle-bg {
  fill: none;
  stroke: var(--color-gray-200);
  stroke-width: 8;
}

.progress-circle-bar {
  fill: none;
  stroke: var(--color-primary-500);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dasharray var(--transition-base);
}

.progress-circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
}

/* 多段进度条 */
.progress-stacked {
  display: flex;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-stacked-segment {
  height: 100%;
  transition: width var(--transition-base);
}

/* 进度条状态 */
.progress-success .progress-bar {
  background-color: var(--color-success-500);
}

.progress-warning .progress-bar {
  background-color: var(--color-warning-500);
}

.progress-danger .progress-bar {
  background-color: var(--color-danger-500);
}

.progress-info .progress-bar {
  background-color: var(--color-info-500);
}

/* 系统状态进度条 */
.system-progress {
  margin-bottom: var(--space-4);
}

.system-progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.system-progress-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.system-progress-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--text-muted);
}

.system-progress-bar {
  height: 6px;
  background-color: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.system-progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
}

/* CPU使用率进度条 */
.cpu-progress .system-progress-fill {
  background: linear-gradient(90deg, var(--color-primary-400), var(--color-primary-600));
}

/* 内存使用率进度条 */
.memory-progress .system-progress-fill {
  background: linear-gradient(90deg, var(--color-warning-400), var(--color-warning-600));
}

/* 磁盘使用率进度条 */
.disk-progress .system-progress-fill {
  background: linear-gradient(90deg, var(--color-info-400), var(--color-info-600));
}

/* 网络使用率进度条 */
.network-progress .system-progress-fill {
  background: linear-gradient(90deg, var(--color-success-400), var(--color-success-600));
}

/* 进度条工具类 */
.progress-0 { width: 0%; }
.progress-5 { width: 5%; }
.progress-10 { width: 10%; }
.progress-15 { width: 15%; }
.progress-20 { width: 20%; }
.progress-25 { width: 25%; }
.progress-30 { width: 30%; }
.progress-35 { width: 35%; }
.progress-40 { width: 40%; }
.progress-45 { width: 45%; }
.progress-50 { width: 50%; }
.progress-55 { width: 55%; }
.progress-60 { width: 60%; }
.progress-65 { width: 65%; }
.progress-70 { width: 70%; }
.progress-75 { width: 75%; }
.progress-80 { width: 80%; }
.progress-85 { width: 85%; }
.progress-90 { width: 90%; }
.progress-95 { width: 95%; }
.progress-100 { width: 100%; }

/* 响应式调整 */
@media (max-width: 768px) {
  .progress-circle {
    width: 60px;
    height: 60px;
  }
  
  .progress-circle-lg {
    width: 80px;
    height: 80px;
  }
  
  .progress-circle-xl {
    width: 100px;
    height: 100px;
  }
  
  .progress-label {
    font-size: var(--font-size-xs);
  }
}
