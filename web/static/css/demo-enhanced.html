<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 CSS重构演示 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="./main-new.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            margin: 30px;
            padding: 50px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .demo-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
            border-radius: 27px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }
        
        @keyframes borderGlow {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 60px;
            padding-bottom: 40px;
            position: relative;
        }
        
        .demo-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }
        
        .demo-header h1 {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            animation: titlePulse 2s ease-in-out infinite;
        }
        
        @keyframes titlePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .demo-header p {
            color: #6c757d;
            font-size: 1.3rem;
            margin: 0;
            font-weight: 300;
        }
        
        .demo-section { 
            margin-bottom: 60px; 
            padding: 40px; 
            background: white;
            border-radius: 20px; 
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .demo-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .demo-section:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        .demo-section:hover::before {
            opacity: 1;
        }
        
        .demo-title { 
            color: #667eea; 
            margin-bottom: 30px; 
            font-weight: 700;
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .demo-title::before {
            content: '';
            width: 6px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px;
        }
        
        .feature-badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-left: auto;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            animation: badgePulse 2s ease-in-out infinite;
        }
        
        @keyframes badgePulse {
            0%, 100% { transform: scale(1); box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); }
            50% { transform: scale(1.05); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4); }
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .highlight-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .improvement-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: transform 0.3s ease;
        }
        
        .improvement-item:hover {
            transform: translateX(10px);
        }
        
        .improvement-item:last-child {
            border-bottom: none;
        }
        
        .improvement-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .improvement-icon.success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .improvement-icon.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .improvement-icon.info {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
            color: white;
        }
        
        .code-preview {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin-top: 25px;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            color: #495057;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .floating-element {
            position: absolute;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .floating-element:nth-child(3) { bottom: 10%; left: 20%; animation-delay: 2s; }
        .floating-element:nth-child(4) { bottom: 20%; right: 20%; animation-delay: 3s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* 响应式优化 */
        @media (max-width: 768px) {
            .demo-container {
                margin: 15px;
                padding: 30px 20px;
            }
            
            .demo-header h1 {
                font-size: 2.5rem;
            }
            
            .demo-section {
                padding: 25px 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>

    <div class="demo-container">
        <div class="demo-header">
            <h1>🎨 CSS重构演示</h1>
            <p>现代化的蜜罐管理平台样式系统</p>
        </div>
        
        <!-- 重构成果展示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                ✨ 重构成果概览
                <span class="feature-badge">100% 无内联样式</span>
            </h3>
            <div class="highlight-box">
                <h4 style="color: #667eea; margin-bottom: 30px; font-size: 1.5rem;">🎯 主要改进</h4>
                <div class="row">
                    <div class="col-md-4">
                        <div class="improvement-item">
                            <div class="improvement-icon success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div>
                                <strong>移除内联样式</strong><br>
                                <small class="text-muted">所有 style="" 属性已清除</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="improvement-item">
                            <div class="improvement-icon info">
                                <i class="bi bi-palette"></i>
                            </div>
                            <div>
                                <strong>统一设计系统</strong><br>
                                <small class="text-muted">CSS变量 + 组件化架构</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="improvement-item">
                            <div class="improvement-icon warning">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            <div>
                                <strong>性能优化</strong><br>
                                <small class="text-muted">模块化CSS + 响应式设计</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片演示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                📊 统计卡片组件
                <span class="feature-badge">新设计</span>
            </h3>
            <div class="code-preview">
                <strong>旧版本:</strong> &lt;i style="font-size: 2rem;"&gt;<br>
                <strong>新版本:</strong> &lt;i class="stat-icon stat-icon-primary"&gt;
            </div>
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="stat-card stat-card-primary">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">在线节点</div>
                                <div class="stat-card-value">24</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-hdd-network stat-icon stat-icon-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-success">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">活跃部署</div>
                                <div class="stat-card-value">156</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-play-circle stat-icon stat-icon-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-warning">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">今日攻击</div>
                                <div class="stat-card-value">89</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-shield-exclamation stat-icon stat-icon-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-info">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">可用模板</div>
                                <div class="stat-card-value">12</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-layers stat-icon stat-icon-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮演示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                🔘 按钮组件
                <span class="feature-badge">悬停效果</span>
            </h3>
            <div class="row">
                <div class="col-md-12 text-center">
                    <button class="btn btn-primary me-3 mb-3">
                        <i class="bi bi-plus btn-icon"></i>主要按钮
                    </button>
                    <button class="btn btn-outline-primary me-3 mb-3">
                        <i class="bi bi-edit btn-icon"></i>次要按钮
                    </button>
                    <button class="btn btn-success me-3 mb-3">
                        <i class="bi bi-check btn-icon"></i>成功按钮
                    </button>
                    <button class="btn btn-outline-warning me-3 mb-3">
                        <i class="bi bi-exclamation-triangle btn-icon"></i>警告按钮
                    </button>
                    <button class="btn btn-outline-danger mb-3">
                        <i class="bi bi-trash btn-icon"></i>危险按钮
                    </button>
                </div>
            </div>
        </div>

        <!-- 进度条演示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                📈 进度条组件
                <span class="feature-badge">动态更新</span>
            </h3>
            <div class="code-preview">
                <strong>旧版本:</strong> &lt;div style="width: 45%"&gt;<br>
                <strong>新版本:</strong> &lt;div class="progress-45"&gt;
            </div>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="system-progress cpu-progress">
                        <div class="system-progress-label">
                            <span class="system-progress-name">CPU使用率</span>
                            <span class="system-progress-value">45%</span>
                        </div>
                        <div class="system-progress-bar">
                            <div class="system-progress-fill progress-45" id="demo-cpu"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="system-progress memory-progress">
                        <div class="system-progress-label">
                            <span class="system-progress-name">内存使用率</span>
                            <span class="system-progress-value">68%</span>
                        </div>
                        <div class="system-progress-bar">
                            <div class="system-progress-fill progress-70" id="demo-memory"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="demo-section">
            <h3 class="demo-title">
                🎉 重构完成
                <span class="feature-badge">Ready</span>
            </h3>
            <div class="highlight-box">
                <h4 style="color: #667eea; margin-bottom: 30px; font-size: 1.5rem;">✨ 成果总结</h4>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div style="font-size: 3rem; color: #28a745; font-weight: 800;">100%</div>
                        <div style="font-weight: 600;">移除内联样式</div>
                    </div>
                    <div class="col-md-3">
                        <div style="font-size: 3rem; color: #667eea; font-weight: 800;">50+</div>
                        <div style="font-weight: 600;">CSS变量定义</div>
                    </div>
                    <div class="col-md-3">
                        <div style="font-size: 3rem; color: #ffc107; font-weight: 800;">8</div>
                        <div style="font-weight: 600;">组件模块</div>
                    </div>
                    <div class="col-md-3">
                        <div style="font-size: 3rem; color: #17a2b8; font-weight: 800;">100%</div>
                        <div style="font-weight: 600;">响应式支持</div>
                    </div>
                </div>
                <p class="mt-4 mb-0" style="color: #6c757d; font-size: 1.1rem;">
                    🚀 现在您的项目拥有了现代化、可维护的CSS架构！
                </p>
            </div>
        </div>
    </div>

    <script>
        // 增强的动画演示
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载动画
            const container = document.querySelector('.demo-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(50px)';

            setTimeout(() => {
                container.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);

            // 为所有demo-section添加进入动画
            const sections = document.querySelectorAll('.demo-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(50px)';
                section.style.transition = `all 0.8s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.1}s`;
                observer.observe(section);
            });

            // 统计卡片悬停效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.05)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 进度条动画
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.system-progress-fill');
                progressBars.forEach(bar => {
                    const width = bar.classList.toString().match(/progress-(\d+)/);
                    if (width) {
                        bar.style.width = '0%';
                        setTimeout(() => {
                            bar.style.transition = 'width 2.5s cubic-bezier(0.4, 0, 0.2, 1)';
                            bar.style.width = width[1] + '%';
                        }, 200);
                    }
                });
            }, 1000);

            // 按钮点击波纹效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255,255,255,0.6);
                        transform: scale(0);
                        animation: ripple 0.8s linear;
                        left: ${x}px;
                        top: ${y}px;
                        width: ${size}px;
                        height: ${size}px;
                    `;

                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 800);
                });
            });

            // 数字计数动画
            const countElements = document.querySelectorAll('[style*="font-size: 3rem"]');
            countElements.forEach(el => {
                const target = parseInt(el.textContent);
                if (!isNaN(target)) {
                    let current = 0;
                    const increment = target / 50;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        el.textContent = Math.floor(current) + (el.textContent.includes('%') ? '%' : '');
                    }, 50);
                }
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
