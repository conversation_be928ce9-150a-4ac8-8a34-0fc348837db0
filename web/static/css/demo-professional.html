<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业管理面板 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="./main-new.css" rel="stylesheet">
    <style>
        /* 专业严肃 + 现代简洁 + 安全可靠 配色方案 */
        :root {
            /* 主色调 - 深蓝灰色系，体现专业和安全 */
            --color-primary-50: #f8fafc;
            --color-primary-100: #f1f5f9;
            --color-primary-200: #e2e8f0;
            --color-primary-300: #cbd5e1;
            --color-primary-400: #94a3b8;
            --color-primary-500: #64748b;
            --color-primary-600: #475569;
            --color-primary-700: #334155;
            --color-primary-800: #1e293b;
            --color-primary-900: #0f172a;
            
            /* 强调色 - 专业蓝色 */
            --color-accent-500: #2563eb;
            --color-accent-600: #1d4ed8;
            --color-accent-700: #1e40af;
            
            /* 状态色 - 简洁实用 */
            --color-success: #059669;
            --color-warning: #d97706;
            --color-danger: #dc2626;
            --color-info: #0284c7;
            
            /* 背景色 */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-sidebar: #1e293b;
            --bg-header: #ffffff;
            --bg-card: #ffffff;
            
            /* 文字颜色 */
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --text-light: #94a3b8;
            --text-white: #ffffff;
            
            /* 边框颜色 */
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            
            /* 阴影 - 极简风格 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        body {
            background: var(--bg-secondary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5;
        }
        
        .demo-container {
            background: var(--bg-primary);
            margin: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .demo-header {
            background: var(--bg-header);
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-primary);
            box-shadow: var(--shadow-sm);
        }
        
        .demo-header h1 {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
            font-size: 20px;
        }
        
        .demo-header .subtitle {
            color: var(--text-muted);
            font-size: 14px;
            margin: 4px 0 0 0;
        }
        
        .demo-layout {
            display: flex;
            flex: 1;
        }
        
        .demo-sidebar {
            width: 240px;
            background: var(--bg-sidebar);
            border-right: 1px solid var(--border-primary);
            padding: 0;
        }
        
        .sidebar-brand {
            padding: 20px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-brand h3 {
            color: var(--text-white);
            font-weight: 600;
            margin: 0;
            font-size: 16px;
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 16px 0;
            margin: 0;
        }
        
        .sidebar-nav-item {
            margin: 0;
        }
        
        .sidebar-nav-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .sidebar-nav-link:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-white);
        }
        
        .sidebar-nav-link.active {
            background: rgba(37, 99, 235, 0.1);
            color: var(--text-white);
            border-right: 2px solid var(--color-accent-500);
        }
        
        .sidebar-nav-icon {
            margin-right: 12px;
            font-size: 16px;
            width: 16px;
            text-align: center;
        }
        
        .demo-main {
            flex: 1;
            padding: 24px;
            background: var(--bg-secondary);
            overflow-y: auto;
        }
        
        .demo-section {
            background: var(--bg-card);
            border-radius: 6px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
            box-shadow: var(--shadow-sm);
        }
        
        .section-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--text-muted);
            font-size: 16px;
        }
        
        /* 统计卡片 - 极简专业风格 */
        .stat-card {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            transition: all 0.15s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            border-color: var(--border-secondary);
            box-shadow: var(--shadow-md);
        }
        
        .stat-card-body {
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .stat-card-content {
            flex: 1;
        }
        
        .stat-card-label {
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 8px;
            color: var(--text-muted);
        }
        
        .stat-card-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }
        
        .stat-card-icon {
            flex-shrink: 0;
            margin-left: 16px;
        }
        
        .stat-icon {
            font-size: 24px;
            color: var(--text-light);
        }
        
        .stat-icon-primary { color: var(--color-accent-500); }
        .stat-icon-success { color: var(--color-success); }
        .stat-icon-warning { color: var(--color-warning); }
        .stat-icon-danger { color: var(--color-danger); }
        .stat-icon-info { color: var(--color-info); }
        
        /* 按钮 - 专业简洁风格 */
        .btn {
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 4px;
            transition: all 0.15s ease;
            border: 1px solid transparent;
        }
        
        .btn-primary {
            background: var(--color-accent-600);
            border-color: var(--color-accent-600);
            color: var(--text-white);
        }
        
        .btn-primary:hover {
            background: var(--color-accent-700);
            border-color: var(--color-accent-700);
            color: var(--text-white);
        }
        
        .btn-outline-primary {
            border-color: var(--color-accent-600);
            color: var(--color-accent-600);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--color-accent-600);
            color: var(--text-white);
        }
        
        .btn-outline-secondary {
            border-color: var(--border-secondary);
            color: var(--text-secondary);
            background: transparent;
        }
        
        .btn-outline-secondary:hover {
            background: var(--color-primary-100);
            border-color: var(--color-primary-300);
            color: var(--text-primary);
        }
        
        /* 进度条 - 简洁实用 */
        .system-progress {
            margin-bottom: 16px;
        }
        
        .system-progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 13px;
        }
        
        .system-progress-name {
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .system-progress-value {
            color: var(--text-muted);
            font-weight: 600;
        }
        
        .system-progress-bar {
            height: 6px;
            background: var(--color-primary-100);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .system-progress-fill {
            height: 100%;
            background: var(--color-accent-500);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-indicator.online {
            background: rgba(5, 150, 105, 0.1);
            color: var(--color-success);
        }
        
        .status-indicator.warning {
            background: rgba(217, 119, 6, 0.1);
            color: var(--color-warning);
        }
        
        .status-indicator.offline {
            background: rgba(220, 38, 38, 0.1);
            color: var(--color-danger);
        }
        
        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }
        
        /* 表格样式 */
        .table {
            font-size: 14px;
        }
        
        .table th {
            background: var(--bg-secondary);
            border-color: var(--border-primary);
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 12px;
        }
        
        .table td {
            border-color: var(--border-primary);
            color: var(--text-primary);
            padding: 12px;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background: var(--color-primary-50);
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .demo-sidebar {
                width: 200px;
            }
            
            .demo-main {
                padding: 16px;
            }
            
            .demo-section {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>蜜罐管理平台</h1>
            <p class="subtitle">专业 · 安全 · 可靠</p>
        </div>
        
        <div class="demo-layout">
            <div class="demo-sidebar">
                <div class="sidebar-brand">
                    <h3>HoneyPot Admin</h3>
                </div>
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link active">
                            <i class="bi bi-grid sidebar-nav-icon"></i>
                            概览
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-hdd-network sidebar-nav-icon"></i>
                            节点管理
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-shield-exclamation sidebar-nav-icon"></i>
                            威胁监控
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-bar-chart sidebar-nav-icon"></i>
                            数据分析
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-file-text sidebar-nav-icon"></i>
                            日志审计
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-gear sidebar-nav-icon"></i>
                            系统设置
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="demo-main">
                <!-- 系统概览 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        <i class="bi bi-speedometer2"></i>
                        系统概览
                    </h3>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">在线节点</div>
                                        <div class="stat-card-value">24</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-hdd-network stat-icon stat-icon-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">活跃部署</div>
                                        <div class="stat-card-value">156</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-play-circle stat-icon stat-icon-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">今日攻击</div>
                                        <div class="stat-card-value">89</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-shield-exclamation stat-icon stat-icon-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">可用模板</div>
                                        <div class="stat-card-value">12</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-layers stat-icon stat-icon-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        <i class="bi bi-activity"></i>
                        系统状态
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">CPU使用率</span>
                                    <span class="system-progress-value">45%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-45"></div>
                                </div>
                            </div>
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">内存使用率</span>
                                    <span class="system-progress-value">68%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-70"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">磁盘使用率</span>
                                    <span class="system-progress-value">32%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-30"></div>
                                </div>
                            </div>
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">网络负载</span>
                                    <span class="system-progress-value">15%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-15"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 节点状态 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        <i class="bi bi-hdd-network"></i>
                        节点状态
                    </h3>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>节点名称</th>
                                    <th>IP地址</th>
                                    <th>状态</th>
                                    <th>类型</th>
                                    <th>最后活动</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>honeypot-web-01</strong></td>
                                    <td>*************</td>
                                    <td>
                                        <span class="status-indicator online">
                                            <span class="status-dot"></span>
                                            在线
                                        </span>
                                    </td>
                                    <td>Web服务</td>
                                    <td>2分钟前</td>
                                    <td>
                                        <button class="btn btn-outline-secondary btn-sm me-1">查看</button>
                                        <button class="btn btn-outline-primary btn-sm">配置</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>honeypot-ssh-02</strong></td>
                                    <td>192.168.1.102</td>
                                    <td>
                                        <span class="status-indicator warning">
                                            <span class="status-dot"></span>
                                            警告
                                        </span>
                                    </td>
                                    <td>SSH服务</td>
                                    <td>15分钟前</td>
                                    <td>
                                        <button class="btn btn-outline-secondary btn-sm me-1">查看</button>
                                        <button class="btn btn-outline-primary btn-sm">配置</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>honeypot-ftp-03</strong></td>
                                    <td>192.168.1.103</td>
                                    <td>
                                        <span class="status-indicator offline">
                                            <span class="status-dot"></span>
                                            离线
                                        </span>
                                    </td>
                                    <td>FTP服务</td>
                                    <td>1小时前</td>
                                    <td>
                                        <button class="btn btn-outline-secondary btn-sm me-1">查看</button>
                                        <button class="btn btn-outline-primary btn-sm">配置</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 操作面板 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        <i class="bi bi-sliders"></i>
                        操作面板
                    </h3>
                    <div class="row">
                        <div class="col-md-12">
                            <button class="btn btn-primary me-2 mb-2">
                                <i class="bi bi-plus-circle me-1"></i>新建蜜罐
                            </button>
                            <button class="btn btn-outline-primary me-2 mb-2">
                                <i class="bi bi-gear me-1"></i>系统配置
                            </button>
                            <button class="btn btn-outline-secondary me-2 mb-2">
                                <i class="bi bi-download me-1"></i>导出数据
                            </button>
                            <button class="btn btn-outline-secondary me-2 mb-2">
                                <i class="bi bi-file-text me-1"></i>生成报告
                            </button>
                            <button class="btn btn-outline-secondary mb-2">
                                <i class="bi bi-arrow-clockwise me-1"></i>刷新状态
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 重构说明 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        <i class="bi bi-check-circle"></i>
                        重构完成
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5 style="color: var(--text-primary); margin-bottom: 12px; font-size: 14px; font-weight: 600;">设计原则</h5>
                            <ul style="list-style: none; padding: 0; margin: 0; color: var(--text-secondary); font-size: 14px;">
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>专业严肃的视觉风格
                                </li>
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>现代简洁的界面设计
                                </li>
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>安全可靠的配色方案
                                </li>
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>优秀的可读性和对比度
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 style="color: var(--text-primary); margin-bottom: 12px; font-size: 14px; font-weight: 600;">技术实现</h5>
                            <ul style="list-style: none; padding: 0; margin: 0; color: var(--text-secondary); font-size: 14px;">
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>100% 移除内联样式
                                </li>
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>CSS变量统一管理
                                </li>
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>组件化CSS架构
                                </li>
                                <li style="padding: 4px 0; display: flex; align-items: center;">
                                    <i class="bi bi-check text-success me-2"></i>响应式设计支持
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简洁的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 进度条动画
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.system-progress-fill');
                progressBars.forEach(bar => {
                    const width = bar.classList.toString().match(/progress-(\d+)/);
                    if (width) {
                        bar.style.width = '0%';
                        setTimeout(() => {
                            bar.style.width = width[1] + '%';
                        }, 100);
                    }
                });
            }, 500);

            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                });
            });

            // 表格行悬停效果已在CSS中定义
        });
    </script>
</body>
</html>
