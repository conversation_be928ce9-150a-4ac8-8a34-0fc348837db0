<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ 安全主题演示 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="./main-new.css" rel="stylesheet">
    <style>
        /* 安全主题配色 */
        :root {
            /* 主色调 - 安全绿 */
            --color-primary-50: #f0f9f6;
            --color-primary-100: #dcf4e8;
            --color-primary-200: #bbe8d2;
            --color-primary-300: #86d5b1;
            --color-primary-400: #4ade80;
            --color-primary-500: #059669;
            --color-primary-600: #047857;
            --color-primary-700: #065f46;
            --color-primary-800: #064e3b;
            --color-primary-900: #022c22;
            
            /* 背景色 */
            --bg-primary: #f0f9f6;
            --bg-secondary: #ffffff;
            --bg-sidebar: #1e3a2f;
            --bg-header: #ffffff;
            
            /* 文字颜色 */
            --text-primary: #064e3b;
            --text-secondary: #047857;
            --text-muted: #6b7280;
            --text-light: #9ca3af;
            --text-white: #ffffff;
            
            /* 边框颜色 */
            --border-primary: #d1e7dd;
            --border-secondary: #e5e7eb;
            
            /* 阴影 */
            --shadow-sm: 0 1px 3px rgba(6, 78, 59, 0.1);
            --shadow-md: 0 4px 12px rgba(6, 78, 59, 0.15);
            --shadow-lg: 0 10px 25px rgba(6, 78, 59, 0.2);
        }
        
        body {
            background: var(--bg-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
        }
        
        .demo-container {
            background: var(--bg-secondary);
            margin: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-md);
            overflow: hidden;
            min-height: calc(100vh - 40px);
        }
        
        .demo-header {
            background: var(--bg-header);
            padding: 20px 30px;
            border-bottom: 2px solid var(--border-primary);
            text-align: center;
        }
        
        .demo-header h1 {
            color: var(--color-primary-700);
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .demo-header .subtitle {
            color: var(--text-muted);
            font-size: 1.1rem;
        }
        
        .demo-layout {
            display: flex;
            min-height: calc(100vh - 120px);
        }
        
        .demo-sidebar {
            width: 250px;
            background: var(--bg-sidebar);
            padding: 30px 0;
            color: var(--text-white);
        }
        
        .sidebar-brand {
            padding: 0 30px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }
        
        .sidebar-brand h3 {
            color: var(--text-white);
            font-weight: 600;
            margin: 0;
            font-size: 1.3rem;
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-nav-item {
            margin-bottom: 5px;
        }
        
        .sidebar-nav-link {
            display: flex;
            align-items: center;
            padding: 12px 30px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }
        
        .sidebar-nav-link:hover,
        .sidebar-nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-white);
            border-left-color: var(--color-primary-400);
        }
        
        .sidebar-nav-icon {
            margin-right: 12px;
            font-size: 1.1rem;
        }
        
        .demo-main {
            flex: 1;
            padding: 30px;
            background: var(--bg-primary);
        }
        
        .demo-section {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid var(--border-primary);
            box-shadow: var(--shadow-sm);
        }
        
        .section-title {
            color: var(--color-primary-700);
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: var(--color-primary-500);
            border-radius: 2px;
        }
        
        /* 重写统计卡片为安全主题 */
        .stat-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--color-primary-500);
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }
        
        .stat-card-primary::before { background: var(--color-primary-500); }
        .stat-card-success::before { background: #10b981; }
        .stat-card-warning::before { background: #f59e0b; }
        .stat-card-danger::before { background: #ef4444; }
        .stat-card-info::before { background: #3b82f6; }
        
        .stat-card-body {
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .stat-card-content {
            flex: 1;
        }
        
        .stat-card-label {
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 8px;
            color: var(--text-muted);
        }
        
        .stat-card-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }
        
        .stat-card-icon {
            flex-shrink: 0;
            margin-left: 15px;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            color: var(--color-primary-500);
        }
        
        .stat-icon-success { color: #10b981; }
        .stat-icon-warning { color: #f59e0b; }
        .stat-icon-danger { color: #ef4444; }
        .stat-icon-info { color: #3b82f6; }
        
        /* 按钮安全主题 */
        .btn-primary {
            background: var(--color-primary-600);
            border-color: var(--color-primary-600);
            color: var(--text-white);
        }
        
        .btn-primary:hover {
            background: var(--color-primary-700);
            border-color: var(--color-primary-700);
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .btn-outline-primary {
            border-color: var(--color-primary-600);
            color: var(--color-primary-600);
        }
        
        .btn-outline-primary:hover {
            background: var(--color-primary-600);
            border-color: var(--color-primary-600);
            color: var(--text-white);
        }
        
        /* 进度条安全主题 */
        .system-progress {
            margin-bottom: 20px;
        }
        
        .system-progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .system-progress-name {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .system-progress-value {
            color: var(--text-muted);
            font-weight: 600;
        }
        
        .system-progress-bar {
            height: 8px;
            background: var(--color-primary-100);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .system-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary-500), var(--color-primary-600));
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        /* 安全状态指示器 */
        .security-status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .security-status.secure {
            background: var(--color-primary-100);
            color: var(--color-primary-700);
        }
        
        .security-status.warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .security-status.danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, var(--color-primary-50), var(--color-primary-100));
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            color: var(--text-primary);
        }
        
        .feature-list li::before {
            content: '✓';
            color: var(--color-primary-600);
            font-weight: bold;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🛡️ 安全主题演示</h1>
            <p class="subtitle">专为蜜罐管理平台设计的安全主题配色方案</p>
        </div>
        
        <div class="demo-layout">
            <div class="demo-sidebar">
                <div class="sidebar-brand">
                    <h3>🍯 蜜罐管理</h3>
                </div>
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link active">
                            <i class="bi bi-speedometer2 sidebar-nav-icon"></i>
                            仪表盘
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-hdd-network sidebar-nav-icon"></i>
                            节点管理
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-shield-exclamation sidebar-nav-icon"></i>
                            攻击监控
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-bar-chart sidebar-nav-icon"></i>
                            数据分析
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link">
                            <i class="bi bi-gear sidebar-nav-icon"></i>
                            系统设置
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="demo-main">
                <!-- 统计卡片演示 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        📊 系统状态概览
                    </h3>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card stat-card-primary">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">在线节点</div>
                                        <div class="stat-card-value">24</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-hdd-network stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card stat-card-success">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">活跃部署</div>
                                        <div class="stat-card-value">156</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-play-circle stat-icon stat-icon-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card stat-card-warning">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">今日攻击</div>
                                        <div class="stat-card-value">89</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-shield-exclamation stat-icon stat-icon-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card stat-card-info">
                                <div class="stat-card-body">
                                    <div class="stat-card-content">
                                        <div class="stat-card-label">可用模板</div>
                                        <div class="stat-card-value">12</div>
                                    </div>
                                    <div class="stat-card-icon">
                                        <i class="bi bi-layers stat-icon stat-icon-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全状态演示 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        🛡️ 安全状态监控
                    </h3>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-highlight">
                                <h5 style="color: var(--color-primary-700); margin-bottom: 15px;">系统安全状态</h5>
                                <div class="security-status secure">
                                    <i class="bi bi-shield-check"></i>
                                    安全运行
                                </div>
                                <p style="margin-top: 15px; color: var(--text-muted); font-size: 0.9rem;">
                                    所有安全检查通过，系统运行正常
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-highlight">
                                <h5 style="color: var(--color-primary-700); margin-bottom: 15px;">威胁检测</h5>
                                <div class="security-status warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    中等风险
                                </div>
                                <p style="margin-top: 15px; color: var(--text-muted); font-size: 0.9rem;">
                                    检测到可疑活动，建议关注
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-highlight">
                                <h5 style="color: var(--color-primary-700); margin-bottom: 15px;">防护等级</h5>
                                <div class="security-status secure">
                                    <i class="bi bi-shield-fill"></i>
                                    高级防护
                                </div>
                                <p style="margin-top: 15px; color: var(--text-muted); font-size: 0.9rem;">
                                    多层防护机制已启用
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统资源监控 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        📈 系统资源监控
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">CPU使用率</span>
                                    <span class="system-progress-value">45%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-45"></div>
                                </div>
                            </div>
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">内存使用率</span>
                                    <span class="system-progress-value">68%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-70"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">磁盘使用率</span>
                                    <span class="system-progress-value">32%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-30"></div>
                                </div>
                            </div>
                            <div class="system-progress">
                                <div class="system-progress-label">
                                    <span class="system-progress-name">网络负载</span>
                                    <span class="system-progress-value">15%</span>
                                </div>
                                <div class="system-progress-bar">
                                    <div class="system-progress-fill progress-15"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮演示 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        🔘 操作控制面板
                    </h3>
                    <div class="row">
                        <div class="col-md-12">
                            <button class="btn btn-primary me-3 mb-3">
                                <i class="bi bi-plus-circle me-2"></i>新建蜜罐
                            </button>
                            <button class="btn btn-outline-primary me-3 mb-3">
                                <i class="bi bi-gear me-2"></i>系统配置
                            </button>
                            <button class="btn btn-success me-3 mb-3">
                                <i class="bi bi-play-circle me-2"></i>启动监控
                            </button>
                            <button class="btn btn-outline-warning me-3 mb-3">
                                <i class="bi bi-pause-circle me-2"></i>暂停服务
                            </button>
                            <button class="btn btn-outline-danger mb-3">
                                <i class="bi bi-stop-circle me-2"></i>停止所有
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主题特色说明 -->
                <div class="demo-section">
                    <h3 class="section-title">
                        🎨 安全主题特色
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-highlight">
                                <h5 style="color: var(--color-primary-700); margin-bottom: 15px;">设计理念</h5>
                                <ul class="feature-list">
                                    <li>绿色调传达安全、稳定的感觉</li>
                                    <li>深色侧边栏提供专业外观</li>
                                    <li>清晰的层次结构和信息组织</li>
                                    <li>适合长时间使用的配色方案</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-highlight">
                                <h5 style="color: var(--color-primary-700); margin-bottom: 15px;">技术优势</h5>
                                <ul class="feature-list">
                                    <li>100% 移除内联样式</li>
                                    <li>CSS变量统一管理颜色</li>
                                    <li>组件化的CSS架构</li>
                                    <li>响应式设计支持</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏导航动画
            const navItems = document.querySelectorAll('.sidebar-nav-item');
            navItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-20px)';
                setTimeout(() => {
                    item.style.transition = 'all 0.4s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 100);
            });

            // 主体内容动画
            const sections = document.querySelectorAll('.demo-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(section);
            });

            // 统计卡片悬停效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 进度条动画
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.system-progress-fill');
                progressBars.forEach(bar => {
                    const width = bar.classList.toString().match(/progress-(\d+)/);
                    if (width) {
                        bar.style.width = '0%';
                        setTimeout(() => {
                            bar.style.transition = 'width 2s ease-in-out';
                            bar.style.width = width[1] + '%';
                        }, 200);
                    }
                });
            }, 1000);

            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    // 简单的点击反馈
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
