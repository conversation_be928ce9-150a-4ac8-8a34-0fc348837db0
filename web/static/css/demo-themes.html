<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 颜色主题选择 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .theme-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .theme-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .theme-header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .theme-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
            border: 3px solid transparent;
        }
        
        .theme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .theme-card.selected {
            border-color: #007bff;
        }
        
        .theme-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .theme-preview {
            height: 200px;
            border-radius: 10px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .theme-colors {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .color-dot {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .theme-description {
            color: #6c757d;
            font-size: 0.9rem;
            text-align: center;
        }
        
        .apply-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 30px auto;
        }
        
        .apply-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        /* 主题1: 商务蓝 */
        .theme-1 {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }
        
        /* 主题2: 优雅灰 */
        .theme-2 {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        
        /* 主题3: 森林绿 */
        .theme-3 {
            background: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
        }
        
        /* 主题4: 暖橙色 */
        .theme-4 {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
        }
        
        /* 主题5: 深紫色 */
        .theme-5 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 主题6: 科技蓝绿 */
        .theme-6 {
            background: linear-gradient(135deg, #00c9ff 0%, #92fe9d 100%);
        }
    </style>
</head>
<body>
    <div class="theme-container">
        <div class="theme-header">
            <h1>🎨 选择您喜欢的颜色主题</h1>
            <p>为蜜罐管理平台选择一个合适的颜色方案</p>
        </div>
        
        <div class="theme-grid">
            <!-- 主题1: 商务蓝 -->
            <div class="theme-card" data-theme="business-blue">
                <h3 class="theme-title">💼 商务蓝调</h3>
                <div class="theme-preview theme-1"></div>
                <div class="theme-colors">
                    <div class="color-dot" style="background: #1e3c72;"></div>
                    <div class="color-dot" style="background: #2a5298;"></div>
                    <div class="color-dot" style="background: #3498db;"></div>
                    <div class="color-dot" style="background: #74b9ff;"></div>
                </div>
                <div class="theme-description">
                    专业、稳重、可信赖的商务风格，适合企业级应用
                </div>
            </div>
            
            <!-- 主题2: 优雅灰 -->
            <div class="theme-card" data-theme="elegant-gray">
                <h3 class="theme-title">🖤 优雅灰调</h3>
                <div class="theme-preview theme-2"></div>
                <div class="theme-colors">
                    <div class="color-dot" style="background: #2c3e50;"></div>
                    <div class="color-dot" style="background: #34495e;"></div>
                    <div class="color-dot" style="background: #7f8c8d;"></div>
                    <div class="color-dot" style="background: #95a5a6;"></div>
                </div>
                <div class="theme-description">
                    简约、现代、高端的设计风格，突出内容本身
                </div>
            </div>
            
            <!-- 主题3: 森林绿 -->
            <div class="theme-card" data-theme="forest-green">
                <h3 class="theme-title">🌲 森林绿调</h3>
                <div class="theme-preview theme-3"></div>
                <div class="theme-colors">
                    <div class="color-dot" style="background: #134e5e;"></div>
                    <div class="color-dot" style="background: #71b280;"></div>
                    <div class="color-dot" style="background: #27ae60;"></div>
                    <div class="color-dot" style="background: #2ecc71;"></div>
                </div>
                <div class="theme-description">
                    自然、安全、稳定的感觉，适合安全相关应用
                </div>
            </div>
            
            <!-- 主题4: 暖橙色 -->
            <div class="theme-card" data-theme="warm-orange">
                <h3 class="theme-title">🔥 暖橙调</h3>
                <div class="theme-preview theme-4"></div>
                <div class="theme-colors">
                    <div class="color-dot" style="background: #ff6b6b;"></div>
                    <div class="color-dot" style="background: #ffa726;"></div>
                    <div class="color-dot" style="background: #e67e22;"></div>
                    <div class="color-dot" style="background: #f39c12;"></div>
                </div>
                <div class="theme-description">
                    活力、温暖、友好的设计，增加用户亲和力
                </div>
            </div>
            
            <!-- 主题5: 深紫色 -->
            <div class="theme-card" data-theme="deep-purple">
                <h3 class="theme-title">💜 深紫调</h3>
                <div class="theme-preview theme-5"></div>
                <div class="theme-colors">
                    <div class="color-dot" style="background: #667eea;"></div>
                    <div class="color-dot" style="background: #764ba2;"></div>
                    <div class="color-dot" style="background: #9b59b6;"></div>
                    <div class="color-dot" style="background: #8e44ad;"></div>
                </div>
                <div class="theme-description">
                    神秘、高贵、创新的感觉，适合科技产品
                </div>
            </div>
            
            <!-- 主题6: 科技蓝绿 -->
            <div class="theme-card" data-theme="tech-cyan">
                <h3 class="theme-title">⚡ 科技蓝绿</h3>
                <div class="theme-preview theme-6"></div>
                <div class="theme-colors">
                    <div class="color-dot" style="background: #00c9ff;"></div>
                    <div class="color-dot" style="background: #92fe9d;"></div>
                    <div class="color-dot" style="background: #1abc9c;"></div>
                    <div class="color-dot" style="background: #16a085;"></div>
                </div>
                <div class="theme-description">
                    未来感、科技感强烈，适合现代化技术平台
                </div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="apply-button" onclick="applySelectedTheme()">
                应用选中的主题
            </button>
            <p style="color: #6c757d; margin-top: 15px;">
                选择一个主题后点击应用，我将为您生成对应的demo页面
            </p>
        </div>
    </div>
    
    <script>
        let selectedTheme = null;
        
        // 主题选择
        document.querySelectorAll('.theme-card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除之前的选中状态
                document.querySelectorAll('.theme-card').forEach(c => c.classList.remove('selected'));
                // 添加选中状态
                this.classList.add('selected');
                selectedTheme = this.dataset.theme;
            });
        });
        
        // 应用主题
        function applySelectedTheme() {
            if (!selectedTheme) {
                alert('请先选择一个主题！');
                return;
            }
            
            // 这里可以跳转到对应主题的demo页面
            alert(`您选择了: ${selectedTheme}\n\n我将为您生成对应的demo页面！`);
            
            // 实际应用中，这里会调用生成对应主题demo页面的功能
            generateThemeDemo(selectedTheme);
        }
        
        function generateThemeDemo(theme) {
            // 根据选择的主题生成对应的demo页面
            console.log('生成主题:', theme);
            
            // 这里可以发送请求到后端，或者直接跳转到预设的主题页面
            const themeUrls = {
                'business-blue': '/static/css/demo-business-blue.html',
                'elegant-gray': '/static/css/demo-elegant-gray.html',
                'forest-green': '/static/css/demo-forest-green.html',
                'warm-orange': '/static/css/demo-warm-orange.html',
                'deep-purple': '/static/css/demo-deep-purple.html',
                'tech-cyan': '/static/css/demo-tech-cyan.html'
            };
            
            // 提示用户
            const themeName = {
                'business-blue': '商务蓝调',
                'elegant-gray': '优雅灰调',
                'forest-green': '森林绿调',
                'warm-orange': '暖橙调',
                'deep-purple': '深紫调',
                'tech-cyan': '科技蓝绿'
            };
            
            alert(`正在为您生成 "${themeName[theme]}" 主题的demo页面...`);
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.theme-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
