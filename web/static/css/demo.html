<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式重构演示 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="./main-new.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .demo-section { margin-bottom: 40px; padding: 20px; border: 1px solid #e3e6f0; border-radius: 8px; }
        .demo-title { color: #667eea; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="text-center mb-5">🎨 CSS重构演示页面</h1>
        
        <!-- 统计卡片演示 -->
        <div class="demo-section">
            <h3 class="demo-title">📊 统计卡片组件</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card stat-card-primary">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">在线节点</div>
                                <div class="stat-card-value">24</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-hdd-network stat-icon stat-icon-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-success">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">活跃部署</div>
                                <div class="stat-card-value">156</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-play-circle stat-icon stat-icon-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-warning">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">今日攻击</div>
                                <div class="stat-card-value">89</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-shield-exclamation stat-icon stat-icon-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-info">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">可用模板</div>
                                <div class="stat-card-value">12</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-layers stat-icon stat-icon-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进度条演示 -->
        <div class="demo-section">
            <h3 class="demo-title">📈 进度条组件</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="system-progress cpu-progress">
                        <div class="system-progress-label">
                            <span class="system-progress-name">CPU使用率</span>
                            <span class="system-progress-value">45%</span>
                        </div>
                        <div class="system-progress-bar">
                            <div class="system-progress-fill progress-45"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="system-progress memory-progress">
                        <div class="system-progress-label">
                            <span class="system-progress-name">内存使用率</span>
                            <span class="system-progress-value">68%</span>
                        </div>
                        <div class="system-progress-bar">
                            <div class="system-progress-fill progress-70"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🔘 按钮组件</h3>
            <div class="row">
                <div class="col-md-12">
                    <button class="btn btn-primary me-3">
                        <i class="bi bi-plus btn-icon"></i>主要按钮
                    </button>
                    <button class="btn btn-outline-primary me-3">
                        <i class="bi bi-edit btn-icon"></i>次要按钮
                    </button>
                    <button class="btn btn-success me-3">
                        <i class="bi bi-check btn-icon"></i>成功按钮
                    </button>
                    <button class="btn btn-outline-warning me-3">
                        <i class="bi bi-exclamation-triangle btn-icon"></i>警告按钮
                    </button>
                    <button class="btn btn-outline-danger">
                        <i class="bi bi-trash btn-icon"></i>危险按钮
                    </button>
                </div>
            </div>
        </div>

        <!-- 徽章演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🏷️ 徽章组件</h3>
            <div class="row">
                <div class="col-md-12">
                    <span class="badge badge-success me-2">
                        <i class="bi bi-check-circle status-icon"></i>运行中
                    </span>
                    <span class="badge badge-warning me-2">
                        <i class="bi bi-clock status-icon"></i>等待中
                    </span>
                    <span class="badge badge-danger me-2">
                        <i class="bi bi-x-circle status-icon"></i>已停止
                    </span>
                    <span class="badge badge-info me-2">
                        <i class="bi bi-info-circle status-icon"></i>信息
                    </span>
                    <span class="badge badge-secondary">
                        <i class="bi bi-question-circle status-icon"></i>未知
                    </span>
                </div>
            </div>
        </div>

        <!-- 图标演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🎯 图标组件</h3>
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex align-items-center gap-4">
                        <div class="icon-container icon-container-primary icon-container-lg">
                            <i class="bi bi-shield-check icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-success icon-container-lg">
                            <i class="bi bi-check-circle icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-warning icon-container-lg">
                            <i class="bi bi-exclamation-triangle icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-danger icon-container-lg">
                            <i class="bi bi-x-circle icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-info icon-container-lg">
                            <i class="bi bi-info-circle icon-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 卡片演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🃏 卡片组件</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-card">
                        <div class="chart-card-header">
                            <h6 class="chart-card-title">
                                <i class="bi bi-graph-up nav-icon"></i>系统概览
                            </h6>
                        </div>
                        <div class="chart-card-body">
                            <div class="chart-container chart-container-sm">
                                <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                                    <i class="bi bi-graph-up me-2"></i>图表区域
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card info-card-primary">
                        <h6 class="mb-3">
                            <i class="bi bi-info-circle nav-icon"></i>信息卡片
                        </h6>
                        <p class="mb-0">这是一个信息卡片的示例，展示了新的设计系统中的卡片样式。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <p class="text-muted">✨ 所有样式均已移除内联样式，使用统一的CSS类管理</p>
        </div>
    </div>

    <script>
        // 简单的动画演示
        document.addEventListener('DOMContentLoaded', function() {
            // 为统计卡片添加淡入动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
