<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 CSS重构演示 - 蜜罐管理平台</title>
    <link href="../libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="../libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="./main-new.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .demo-header {
            text-align: center;
            margin-bottom: 50px;
            padding-bottom: 30px;
            border-bottom: 2px solid #f0f0f0;
        }

        .demo-header h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .demo-header p {
            color: #6c757d;
            font-size: 1.2rem;
            margin: 0;
        }

        .demo-section {
            margin-bottom: 50px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .demo-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
        }

        .demo-title {
            color: #667eea;
            margin-bottom: 25px;
            font-weight: 600;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-title::before {
            content: '';
            width: 4px;
            height: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .feature-badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: auto;
        }

        .code-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #495057;
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .improvement-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .improvement-item:last-child {
            border-bottom: none;
        }

        .improvement-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .improvement-icon.success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .improvement-icon.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .improvement-icon.info {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎨 CSS重构演示</h1>
            <p>现代化的蜜罐管理平台样式系统</p>
        </div>

        <!-- 重构成果展示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                ✨ 重构成果概览
                <span class="feature-badge">100% 无内联样式</span>
            </h3>
            <div class="highlight-box">
                <h4 style="color: #667eea; margin-bottom: 20px;">🎯 主要改进</h4>
                <div class="row">
                    <div class="col-md-4">
                        <div class="improvement-item">
                            <div class="improvement-icon success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div>
                                <strong>移除内联样式</strong><br>
                                <small class="text-muted">所有 style="" 属性已清除</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="improvement-item">
                            <div class="improvement-icon info">
                                <i class="bi bi-palette"></i>
                            </div>
                            <div>
                                <strong>统一设计系统</strong><br>
                                <small class="text-muted">CSS变量 + 组件化架构</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="improvement-item">
                            <div class="improvement-icon warning">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            <div>
                                <strong>性能优化</strong><br>
                                <small class="text-muted">模块化CSS + 响应式设计</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片演示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                📊 统计卡片组件
                <span class="feature-badge">新设计</span>
            </h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card stat-card-primary">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">在线节点</div>
                                <div class="stat-card-value">24</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-hdd-network stat-icon stat-icon-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-success">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">活跃部署</div>
                                <div class="stat-card-value">156</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-play-circle stat-icon stat-icon-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-warning">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">今日攻击</div>
                                <div class="stat-card-value">89</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-shield-exclamation stat-icon stat-icon-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-card-info">
                        <div class="stat-card-body">
                            <div class="stat-card-content">
                                <div class="stat-card-label">可用模板</div>
                                <div class="stat-card-value">12</div>
                            </div>
                            <div class="stat-card-icon">
                                <i class="bi bi-layers stat-icon stat-icon-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进度条演示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                📈 进度条组件
                <span class="feature-badge">动态更新</span>
            </h3>
            <div class="code-preview">
                <strong>旧版本:</strong> &lt;div style="width: 45%"&gt;<br>
                <strong>新版本:</strong> &lt;div class="progress-45"&gt;
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="system-progress cpu-progress">
                        <div class="system-progress-label">
                            <span class="system-progress-name">CPU使用率</span>
                            <span class="system-progress-value">45%</span>
                        </div>
                        <div class="system-progress-bar">
                            <div class="system-progress-fill progress-45"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="system-progress memory-progress">
                        <div class="system-progress-label">
                            <span class="system-progress-name">内存使用率</span>
                            <span class="system-progress-value">68%</span>
                        </div>
                        <div class="system-progress-bar">
                            <div class="system-progress-fill progress-70"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮演示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                🔘 按钮组件
                <span class="feature-badge">悬停效果</span>
            </h3>
            <div class="row">
                <div class="col-md-12">
                    <button class="btn btn-primary me-3">
                        <i class="bi bi-plus btn-icon"></i>主要按钮
                    </button>
                    <button class="btn btn-outline-primary me-3">
                        <i class="bi bi-edit btn-icon"></i>次要按钮
                    </button>
                    <button class="btn btn-success me-3">
                        <i class="bi bi-check btn-icon"></i>成功按钮
                    </button>
                    <button class="btn btn-outline-warning me-3">
                        <i class="bi bi-exclamation-triangle btn-icon"></i>警告按钮
                    </button>
                    <button class="btn btn-outline-danger">
                        <i class="bi bi-trash btn-icon"></i>危险按钮
                    </button>
                </div>
            </div>
        </div>

        <!-- 徽章演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🏷️ 徽章组件</h3>
            <div class="row">
                <div class="col-md-12">
                    <span class="badge badge-success me-2">
                        <i class="bi bi-check-circle status-icon"></i>运行中
                    </span>
                    <span class="badge badge-warning me-2">
                        <i class="bi bi-clock status-icon"></i>等待中
                    </span>
                    <span class="badge badge-danger me-2">
                        <i class="bi bi-x-circle status-icon"></i>已停止
                    </span>
                    <span class="badge badge-info me-2">
                        <i class="bi bi-info-circle status-icon"></i>信息
                    </span>
                    <span class="badge badge-secondary">
                        <i class="bi bi-question-circle status-icon"></i>未知
                    </span>
                </div>
            </div>
        </div>

        <!-- 图标演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🎯 图标组件</h3>
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex align-items-center gap-4">
                        <div class="icon-container icon-container-primary icon-container-lg">
                            <i class="bi bi-shield-check icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-success icon-container-lg">
                            <i class="bi bi-check-circle icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-warning icon-container-lg">
                            <i class="bi bi-exclamation-triangle icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-danger icon-container-lg">
                            <i class="bi bi-x-circle icon-lg"></i>
                        </div>
                        <div class="icon-container icon-container-info icon-container-lg">
                            <i class="bi bi-info-circle icon-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 卡片演示 -->
        <div class="demo-section">
            <h3 class="demo-title">🃏 卡片组件</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-card">
                        <div class="chart-card-header">
                            <h6 class="chart-card-title">
                                <i class="bi bi-graph-up nav-icon"></i>系统概览
                            </h6>
                        </div>
                        <div class="chart-card-body">
                            <div class="chart-container chart-container-sm">
                                <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                                    <i class="bi bi-graph-up me-2"></i>图表区域
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card info-card-primary">
                        <h6 class="mb-3">
                            <i class="bi bi-info-circle nav-icon"></i>信息卡片
                        </h6>
                        <p class="mb-0">这是一个信息卡片的示例，展示了新的设计系统中的卡片样式。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术栈展示 -->
        <div class="demo-section">
            <h3 class="demo-title">
                🛠️ 技术栈
                <span class="feature-badge">现代化</span>
            </h3>
            <div class="stats-grid">
                <div class="highlight-box">
                    <h5 style="color: #667eea;">CSS架构</h5>
                    <p>CSS变量 + 组件化 + 响应式</p>
                </div>
                <div class="highlight-box">
                    <h5 style="color: #28a745;">兼容性</h5>
                    <p>Bootstrap 5.3 + 现代浏览器</p>
                </div>
                <div class="highlight-box">
                    <h5 style="color: #ffc107;">性能</h5>
                    <p>模块化加载 + 优化动画</p>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="demo-section">
            <h3 class="demo-title">
                🎉 重构完成
                <span class="feature-badge">Ready</span>
            </h3>
            <div class="highlight-box">
                <h4 style="color: #667eea; margin-bottom: 20px;">✨ 成果总结</h4>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div style="font-size: 2rem; color: #28a745;">100%</div>
                        <div>移除内联样式</div>
                    </div>
                    <div class="col-md-3">
                        <div style="font-size: 2rem; color: #667eea;">50+</div>
                        <div>CSS变量定义</div>
                    </div>
                    <div class="col-md-3">
                        <div style="font-size: 2rem; color: #ffc107;">8</div>
                        <div>组件模块</div>
                    </div>
                    <div class="col-md-3">
                        <div style="font-size: 2rem; color: #17a2b8;">100%</div>
                        <div>响应式支持</div>
                    </div>
                </div>
                <p class="mt-4 mb-0" style="color: #6c757d;">
                    🚀 现在您的项目拥有了现代化、可维护的CSS架构！
                </p>
            </div>
        </div>
    </div>

    <script>
        // 增强的动画演示
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有demo-section添加进入动画
            const sections = document.querySelectorAll('.demo-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(section);
            });

            // 统计卡片悬停效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 进度条动画
            const progressBars = document.querySelectorAll('.system-progress-fill');
            progressBars.forEach(bar => {
                const width = bar.classList.toString().match(/progress-(\d+)/);
                if (width) {
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.transition = 'width 2s ease-in-out';
                        bar.style.width = width[1] + '%';
                    }, 500);
                }
            });

            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const ripple = document.createElement('span');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255,255,255,0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = (e.clientX - e.target.offsetLeft) + 'px';
                    ripple.style.top = (e.clientY - e.target.offsetTop) + 'px';
                    ripple.style.width = ripple.style.height = '20px';
                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 600);
                });
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
