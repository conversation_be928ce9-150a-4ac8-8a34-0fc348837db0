/* 蜜罐管理平台 - 主样式文件 */

/* 基础样式 */
@import url('./base/variables.css');
@import url('./base/reset.css');
@import url('./base/typography.css');

/* 组件样式 */
@import url('./components/icons.css');
@import url('./components/cards.css');
@import url('./components/progress.css');

/* 布局样式 */
.container-fluid {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

/* 导航栏样式 */
.navbar {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%) !important;
  box-shadow: var(--shadow-md);
  border: none;
}

.navbar-brand {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  color: white !important;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: var(--font-weight-medium);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  margin: 0 var(--space-1);
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white !important;
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white !important;
  font-weight: var(--font-weight-semibold);
}

/* 页面标题区域 */
.page-header {
  margin-bottom: var(--space-8);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin-bottom: var(--space-2);
}

.page-subtitle {
  color: var(--text-muted);
  font-size: var(--font-size-base);
}

/* 按钮样式增强 */
.btn {
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--space-3) var(--space-5);
  transition: all var(--transition-base);
  border: none;
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
}

.btn-outline-primary {
  border: 2px solid var(--color-primary-500);
  color: var(--color-primary-600);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--color-primary-500);
  color: white;
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
  color: white;
}

.btn-outline-success {
  border: 2px solid var(--color-success-500);
  color: var(--color-success-600);
  background: transparent;
}

.btn-outline-success:hover {
  background: var(--color-success-500);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
  color: white;
}

.btn-outline-warning {
  border: 2px solid var(--color-warning-500);
  color: var(--color-warning-600);
  background: transparent;
}

.btn-outline-warning:hover {
  background: var(--color-warning-500);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--color-danger-500), var(--color-danger-600));
  color: white;
}

.btn-outline-danger {
  border: 2px solid var(--color-danger-500);
  color: var(--color-danger-600);
  background: transparent;
}

.btn-outline-danger:hover {
  background: var(--color-danger-500);
  color: white;
}

.btn-secondary {
  background: var(--color-gray-500);
  color: white;
}

.btn-outline-secondary {
  border: 2px solid var(--color-gray-400);
  color: var(--color-gray-600);
  background: transparent;
}

.btn-outline-secondary:hover {
  background: var(--color-gray-500);
  color: white;
}

/* 表格样式增强 */
.table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background: white;
  border: none;
}

.table thead th {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  border: none;
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  font-size: var(--font-size-xs);
  letter-spacing: 0.05em;
  padding: var(--space-4) var(--space-6);
  color: var(--text-dark);
}

.table tbody tr {
  transition: all var(--transition-fast);
  border: none;
}

.table tbody tr:hover {
  background-color: var(--color-primary-50);
  transform: scale(1.01);
}

.table tbody td {
  padding: var(--space-4) var(--space-6);
  border: none;
  border-bottom: 1px solid var(--color-gray-100);
  vertical-align: middle;
}

/* 徽章样式 */
.badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
}

.badge-success {
  background-color: var(--color-success-100);
  color: var(--color-success-700);
}

.badge-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge-danger {
  background-color: var(--color-danger-100);
  color: var(--color-danger-700);
}

.badge-info {
  background-color: var(--color-info-100);
  color: var(--color-info-700);
}

.badge-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: var(--space-8);
  color: var(--text-muted);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-200);
  border-radius: 50%;
  border-top-color: var(--color-primary-500);
  animation: spin 1s ease-in-out infinite;
  margin-right: var(--space-2);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--space-12) var(--space-4);
  color: var(--text-muted);
}

.empty-state-icon {
  font-size: var(--icon-3xl);
  color: var(--color-gray-300);
  margin-bottom: var(--space-4);
}

.empty-state-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin-bottom: var(--space-2);
}

.empty-state-description {
  color: var(--text-muted);
  margin-bottom: var(--space-6);
}

/* 工具类 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 响应式工具类 */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: var(--space-3);
    padding-right: var(--space-3);
  }
  
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
  }
  
  .page-title {
    font-size: var(--font-size-xl);
  }
  
  .table thead th,
  .table tbody td {
    padding: var(--space-3) var(--space-4);
  }
}
