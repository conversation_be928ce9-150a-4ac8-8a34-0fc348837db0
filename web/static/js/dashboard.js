// 仪表板页面JavaScript

// 全局变量
let overviewChart = null;
let nodeStatusChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    checkAuth();
    
    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
    
    // 定时刷新数据
    setInterval(refreshDashboard, 30000); // 每30秒刷新一次
});

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
    
    // 设置用户信息
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
    if (userInfo.username) {
        const usernameElement = document.getElementById('username');
        if (usernameElement) {
            usernameElement.textContent = userInfo.username;
        }
    }
}

// 初始化页面
function initializePage() {
    // 加载仪表板数据
    loadDashboardData();
    
    // 初始化图表
    initializeCharts();
    
    // 检查服务状态
    checkServiceStatus();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        const response = await fetch('/api/v1/dashboard/overview', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        
        // 更新统计卡片
        updateStatisticsCards(data);
        
        // 更新最近活动
        updateRecentActivities(data.recent_activities || []);
        
    } catch (error) {
        console.error('Load dashboard data failed:', error);
        showError('加载仪表板数据失败: ' + error.message);
    }
}

// 更新统计卡片
function updateStatisticsCards(data) {
    document.getElementById('online-nodes').textContent = data.node_stats?.online || '0';
    document.getElementById('active-deployments').textContent = data.deployment_stats?.running || '0';
    document.getElementById('today-attacks').textContent = data.attack_stats?.today || '0';
    document.getElementById('total-templates').textContent = data.template_stats?.total || '0';
}

// 初始化图表
function initializeCharts() {
    // 系统概览图表
    const overviewCtx = document.getElementById('overview-chart').getContext('2d');
    overviewChart = new Chart(overviewCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '攻击次数',
                data: [],
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }, {
                label: '节点活动',
                data: [],
                borderColor: '#4BC0C0',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 节点状态分布饼图
    const nodeStatusCtx = document.getElementById('node-status-chart').getContext('2d');
    nodeStatusChart = new Chart(nodeStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['在线', '离线', '错误'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 加载图表数据
    loadChartData();
}

// 加载图表数据
async function loadChartData() {
    try {
        // 加载节点状态数据
        const nodesResponse = await fetch('/api/v1/nodes', {
            headers: getAuthHeaders()
        });

        if (nodesResponse.ok) {
            const nodesData = await nodesResponse.json();
            if (nodesData.success) {
                updateNodeStatusChart(nodesData.data);
            }
        }

        // 加载攻击趋势数据（如果有情报数据API）
        try {
            const attacksResponse = await fetch('/api/v1/intelligence/statistics', {
                headers: getAuthHeaders()
            });

            if (attacksResponse.ok) {
                const attacksData = await attacksResponse.json();
                if (attacksData.success) {
                    updateOverviewChart(attacksData.data);
                }
            }
        } catch (error) {
            console.log('Intelligence data not available:', error);
            // 使用默认数据
            updateOverviewChart(null);
        }

    } catch (error) {
        console.error('Load chart data failed:', error);
    }
}

// 更新节点状态图表
function updateNodeStatusChart(nodes) {
    if (!nodeStatusChart || !nodes) return;

    const statusCounts = { online: 0, offline: 0, error: 0 };

    nodes.forEach(node => {
        if (node.status === 'online') {
            statusCounts.online++;
        } else if (node.status === 'offline') {
            statusCounts.offline++;
        } else {
            statusCounts.error++;
        }
    });

    nodeStatusChart.data.datasets[0].data = [
        statusCounts.online,
        statusCounts.offline,
        statusCounts.error
    ];
    nodeStatusChart.update();
}

// 更新系统概览图表
function updateOverviewChart(data) {
    if (!overviewChart) return;

    if (data && data.hourly_stats) {
        // 使用真实数据
        const labels = data.hourly_stats.map(stat => `${stat.hour}:00`);
        const attackData = data.hourly_stats.map(stat => stat.count);

        overviewChart.data.labels = labels;
        overviewChart.data.datasets[0].data = attackData;
        overviewChart.data.datasets[1].data = new Array(labels.length).fill(0); // 节点活动数据暂时为空
    } else {
        // 使用默认数据
        const hours = Array.from({length: 24}, (_, i) => `${i}:00`);
        overviewChart.data.labels = hours;
        overviewChart.data.datasets[0].data = new Array(24).fill(0);
        overviewChart.data.datasets[1].data = new Array(24).fill(0);
    }

    overviewChart.update();
}

// 更新最近活动
function updateRecentActivities(activities) {
    const tbody = document.getElementById('recent-activities');

    if (!activities || activities.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无最近活动</td></tr>';
        return;
    }

    tbody.innerHTML = activities.map(activity => `
        <tr>
            <td>${formatTime(activity.created_at || activity.time)}</td>
            <td>${activity.description || activity.event}</td>
            <td><span class="badge bg-${getStatusColor(activity.status)}">${getStatusText(activity.status)}</span></td>
        </tr>
    `).join('');
}

// 检查服务状态
async function checkServiceStatus() {
    // 检查DecoyWatch状态
    try {
        const response = await fetch('/api/v1/intelligence/health', {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            document.getElementById('decoywatch-status').className = 'badge bg-success';
            document.getElementById('decoywatch-status').textContent = '正常';
        } else {
            throw new Error('Service unavailable');
        }
    } catch (error) {
        document.getElementById('decoywatch-status').className = 'badge bg-danger';
        document.getElementById('decoywatch-status').textContent = '异常';
    }
    
    // 检查WebSocket状态（模拟）
    document.getElementById('websocket-status').className = 'badge bg-success';
    document.getElementById('websocket-status').textContent = '已连接';
    
    // 更新系统资源使用率（模拟）
    updateSystemResources();
}

// 更新系统资源使用率
function updateSystemResources() {
    // 模拟CPU使用率
    const cpuUsage = Math.floor(Math.random() * 30) + 20; // 20-50%
    updateProgressBar('cpu-usage', cpuUsage);
    updateProgressText('cpu-usage-text', cpuUsage);

    // 模拟内存使用率
    const memoryUsage = Math.floor(Math.random() * 40) + 30; // 30-70%
    updateProgressBar('memory-usage', memoryUsage);
    updateProgressText('memory-usage-text', memoryUsage);
}

// 更新进度条工具函数
function updateProgressBar(elementId, percentage) {
    const progressBar = document.getElementById(elementId);
    if (progressBar) {
        // 移除所有进度类
        progressBar.className = progressBar.className.replace(/progress-\d+/g, '');
        // 添加新的进度类
        const progressClass = `progress-${Math.round(percentage / 5) * 5}`; // 四舍五入到最近的5
        progressBar.classList.add('system-progress-fill', progressClass);
    }
}

// 更新进度条文本工具函数
function updateProgressText(elementId, percentage) {
    const textElement = document.getElementById(elementId);
    if (textElement) {
        textElement.textContent = percentage + '%';
    }
}

// 刷新仪表板
function refreshDashboard() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;
    
    Promise.all([
        loadDashboardData(),
        checkServiceStatus()
    ]).finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 获取状态颜色
function getStatusColor(status) {
    switch (status) {
        case 'success': return 'success';
        case 'info': return 'info';
        case 'warning': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'success': return '成功';
        case 'info': return '信息';
        case 'warning': return '警告';
        case 'error': return '错误';
        default: return '未知';
    }
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '未知';

    try {
        const date = new Date(timeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return timeStr;
    }
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }
    
    return data.data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}
