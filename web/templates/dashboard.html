<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/main-new.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-shield-check me-2"></i>
                蜜罐管理平台
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/nodes">
                            <i class="bi bi-hdd-network me-1"></i>节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/templates">
                            <i class="bi bi-layers me-1"></i>模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/deployments">
                            <i class="bi bi-play-circle me-1"></i>部署管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/intelligence">
                            <i class="bi bi-shield-exclamation me-1"></i>情报数据
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="username">管理员</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题和操作按钮 -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="page-title">
                        <i class="bi bi-speedometer2 nav-icon"></i>系统仪表板
                    </h2>
                    <p class="page-subtitle">蜜罐系统运行状态概览</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-secondary" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise btn-icon"></i>刷新数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stat-card stat-card-primary">
                    <div class="stat-card-body">
                        <div class="stat-card-content">
                            <div class="stat-card-label">
                                在线节点
                            </div>
                            <div class="stat-card-value" id="online-nodes">
                                <span class="loading-spinner"></span>加载中...
                            </div>
                        </div>
                        <div class="stat-card-icon">
                            <i class="bi bi-hdd-network stat-icon stat-icon-primary"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stat-card stat-card-success">
                    <div class="stat-card-body">
                        <div class="stat-card-content">
                            <div class="stat-card-label">
                                活跃部署
                            </div>
                            <div class="stat-card-value" id="active-deployments">
                                <span class="loading-spinner"></span>加载中...
                            </div>
                        </div>
                        <div class="stat-card-icon">
                            <i class="bi bi-play-circle stat-icon stat-icon-success"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stat-card stat-card-warning">
                    <div class="stat-card-body">
                        <div class="stat-card-content">
                            <div class="stat-card-label">
                                今日攻击
                            </div>
                            <div class="stat-card-value" id="today-attacks">
                                <span class="loading-spinner"></span>加载中...
                            </div>
                        </div>
                        <div class="stat-card-icon">
                            <i class="bi bi-shield-exclamation stat-icon stat-icon-warning"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stat-card stat-card-info">
                    <div class="stat-card-body">
                        <div class="stat-card-content">
                            <div class="stat-card-label">
                                可用模板
                            </div>
                            <div class="stat-card-value" id="total-templates">
                                <span class="loading-spinner"></span>加载中...
                            </div>
                        </div>
                        <div class="stat-card-icon">
                            <i class="bi bi-layers stat-icon stat-icon-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="chart-card">
                    <div class="chart-card-header">
                        <h6 class="chart-card-title">
                            <i class="bi bi-graph-up nav-icon"></i>系统概览
                        </h6>
                    </div>
                    <div class="chart-card-body">
                        <div class="chart-container">
                            <canvas id="overview-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-card-header">
                        <h6 class="chart-card-title">
                            <i class="bi bi-pie-chart nav-icon"></i>节点状态分布
                        </h6>
                    </div>
                    <div class="chart-card-body">
                        <div class="chart-container chart-container-sm">
                            <canvas id="node-status-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-clock-history me-2"></i>最近活动
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="recent-activities-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>事件</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-activities">
                                    <tr>
                                        <td colspan="3" class="text-center">
                                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-cpu me-2"></i>系统状态
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="system-progress cpu-progress">
                                    <div class="system-progress-label">
                                        <span class="system-progress-name">CPU使用率</span>
                                        <span class="system-progress-value" id="cpu-usage-text">0%</span>
                                    </div>
                                    <div class="system-progress-bar">
                                        <div class="system-progress-fill progress-0" id="cpu-usage"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="system-progress memory-progress">
                                    <div class="system-progress-label">
                                        <span class="system-progress-name">内存使用率</span>
                                        <span class="system-progress-value" id="memory-usage-text">0%</span>
                                    </div>
                                    <div class="system-progress-bar">
                                        <div class="system-progress-fill progress-0" id="memory-usage"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <h6>服务状态</h6>
                                <div id="service-status">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>管理平台</span>
                                        <span class="badge badge-success">
                                            <i class="bi bi-check-circle status-icon"></i>运行中
                                        </span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>DecoyWatch</span>
                                        <span class="badge badge-secondary" id="decoywatch-status">
                                            <i class="bi bi-clock status-icon"></i>检查中...
                                        </span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>WebSocket</span>
                                        <span class="badge badge-info" id="websocket-status">
                                            <i class="bi bi-wifi status-icon"></i>连接中...
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-lightning me-2"></i>快速操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="/nodes" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="bi bi-hdd-network btn-icon"></i>管理节点
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/templates" class="btn btn-outline-success w-100 mb-2">
                                    <i class="bi bi-layers btn-icon"></i>管理模板
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/deployments" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="bi bi-play-circle btn-icon"></i>管理部署
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/intelligence" class="btn btn-outline-danger w-100 mb-2">
                                    <i class="bi bi-shield-exclamation btn-icon"></i>查看情报
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="/static/libs/chart.js/chart.min.js"></script>
    <!-- 自定义JS -->
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
